"""
统一知识库统计服务

提供内外部知识库的统一统计功能，包括查询统计、使用分析等
"""
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from flask import current_app
from app.models import (
    Knowledge, ExternalKnowledge, KnowledgeQueryLog, ExternalKnowledgeQueryLog,
    Role, RoleKnowledge, RoleExternalKnowledge
)
from app.extensions import db
from sqlalchemy import func, desc, and_

logger = logging.getLogger(__name__)

class UnifiedKnowledgeStatisticsService:
    """统一知识库统计服务类"""

    @staticmethod
    def get_overview_statistics() -> Dict[str, Any]:
        """
        获取知识库概览统计
        
        Returns:
            Dict[str, Any]: 概览统计数据
        """
        try:
            # 内部知识库统计
            internal_count = Knowledge.query.count()
            internal_bindings = RoleKnowledge.query.count()
            internal_queries = KnowledgeQueryLog.query.count()
            
            # 外部知识库统计
            external_count = ExternalKnowledge.query.filter_by(status='active').count()
            external_bindings = RoleExternalKnowledge.query.count()
            external_queries = ExternalKnowledgeQueryLog.query.count()
            
            return {
                'success': True,
                'data': {
                    'internal': {
                        'total_knowledges': internal_count,
                        'total_bindings': internal_bindings,
                        'total_queries': internal_queries
                    },
                    'external': {
                        'total_knowledges': external_count,
                        'total_bindings': external_bindings,
                        'total_queries': external_queries
                    },
                    'combined': {
                        'total_knowledges': internal_count + external_count,
                        'total_bindings': internal_bindings + external_bindings,
                        'total_queries': internal_queries + external_queries
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"获取概览统计失败: {str(e)}")
            return {
                'success': False,
                'error_message': f'获取统计失败: {str(e)}'
            }

    @staticmethod
    def get_query_statistics(knowledge_type: str = 'all', 
                           knowledge_id: Optional[int] = None,
                           role_id: Optional[int] = None,
                           days: int = 30) -> Dict[str, Any]:
        """
        获取查询统计信息
        
        Args:
            knowledge_type: 知识库类型 ('internal', 'external', 'all')
            knowledge_id: 知识库ID（可选）
            role_id: 角色ID（可选）
            days: 统计天数
            
        Returns:
            Dict[str, Any]: 查询统计数据
        """
        try:
            start_date = datetime.now() - timedelta(days=days)
            
            # 构建查询条件
            internal_logs = []
            external_logs = []
            
            if knowledge_type in ['internal', 'all']:
                internal_query = KnowledgeQueryLog.query.filter(
                    KnowledgeQueryLog.created_at >= start_date
                )
                if knowledge_id and knowledge_type == 'internal':
                    internal_query = internal_query.filter_by(knowledge_id=knowledge_id)
                if role_id:
                    internal_query = internal_query.filter_by(role_id=role_id)
                internal_logs = internal_query.all()
            
            if knowledge_type in ['external', 'all']:
                external_query = ExternalKnowledgeQueryLog.query.filter(
                    ExternalKnowledgeQueryLog.created_at >= start_date
                )
                if knowledge_id and knowledge_type == 'external':
                    external_query = external_query.filter_by(external_knowledge_id=knowledge_id)
                if role_id:
                    external_query = external_query.filter_by(role_id=role_id)
                external_logs = external_query.all()
            
            # 合并日志数据
            all_logs = internal_logs + external_logs
            
            # 计算统计信息
            total_queries = len(all_logs)
            success_queries = len([log for log in all_logs if log.status == 'success'])
            error_queries = total_queries - success_queries
            
            success_rate = (success_queries / total_queries * 100) if total_queries > 0 else 0
            
            # 计算平均响应时间
            success_logs = [log for log in all_logs if log.status == 'success' and log.query_time]
            avg_response_time = sum(log.query_time for log in success_logs) / len(success_logs) if success_logs else 0
            
            # 按日期分组统计
            daily_stats = {}
            for log in all_logs:
                date_key = log.created_at.strftime('%Y-%m-%d')
                if date_key not in daily_stats:
                    daily_stats[date_key] = {'queries': 0, 'success': 0, 'errors': 0}
                daily_stats[date_key]['queries'] += 1
                if log.status == 'success':
                    daily_stats[date_key]['success'] += 1
                else:
                    daily_stats[date_key]['errors'] += 1
            
            return {
                'success': True,
                'statistics': {
                    'total_queries': total_queries,
                    'success_queries': success_queries,
                    'error_queries': error_queries,
                    'success_rate': round(success_rate, 2),
                    'avg_response_time': round(avg_response_time, 3),
                    'period_days': days,
                    'daily_stats': daily_stats,
                    'internal_queries': len(internal_logs),
                    'external_queries': len(external_logs)
                }
            }
            
        except Exception as e:
            logger.error(f"获取查询统计失败: {str(e)}")
            return {
                'success': False,
                'error_message': f'获取统计失败: {str(e)}'
            }

    @staticmethod
    def get_knowledge_usage_ranking(knowledge_type: str = 'all', 
                                  days: int = 30, 
                                  limit: int = 10) -> Dict[str, Any]:
        """
        获取知识库使用排行
        
        Args:
            knowledge_type: 知识库类型 ('internal', 'external', 'all')
            days: 统计天数
            limit: 返回数量限制
            
        Returns:
            Dict[str, Any]: 使用排行数据
        """
        try:
            start_date = datetime.now() - timedelta(days=days)
            ranking = []
            
            if knowledge_type in ['internal', 'all']:
                # 内部知识库排行
                internal_stats = db.session.query(
                    Knowledge.id,
                    Knowledge.name,
                    func.count(KnowledgeQueryLog.id).label('query_count'),
                    func.avg(KnowledgeQueryLog.query_time).label('avg_time')
                ).outerjoin(KnowledgeQueryLog, and_(
                    Knowledge.id == KnowledgeQueryLog.knowledge_id,
                    KnowledgeQueryLog.created_at >= start_date
                )).group_by(Knowledge.id).order_by(
                    desc('query_count')
                ).limit(limit).all()
                
                for stat in internal_stats:
                    ranking.append({
                        'id': stat.id,
                        'name': stat.name,
                        'type': 'internal',
                        'query_count': stat.query_count or 0,
                        'avg_response_time': round(stat.avg_time or 0, 3)
                    })
            
            if knowledge_type in ['external', 'all']:
                # 外部知识库排行
                external_stats = db.session.query(
                    ExternalKnowledge.id,
                    ExternalKnowledge.name,
                    func.count(ExternalKnowledgeQueryLog.id).label('query_count'),
                    func.avg(ExternalKnowledgeQueryLog.query_time).label('avg_time')
                ).outerjoin(ExternalKnowledgeQueryLog, and_(
                    ExternalKnowledge.id == ExternalKnowledgeQueryLog.external_knowledge_id,
                    ExternalKnowledgeQueryLog.created_at >= start_date
                )).group_by(ExternalKnowledge.id).order_by(
                    desc('query_count')
                ).limit(limit).all()
                
                for stat in external_stats:
                    ranking.append({
                        'id': stat.id,
                        'name': stat.name,
                        'type': 'external',
                        'query_count': stat.query_count or 0,
                        'avg_response_time': round(stat.avg_time or 0, 3)
                    })
            
            # 如果是all类型，需要重新排序
            if knowledge_type == 'all':
                ranking.sort(key=lambda x: x['query_count'], reverse=True)
                ranking = ranking[:limit]
            
            return {
                'success': True,
                'data': ranking
            }
            
        except Exception as e:
            logger.error(f"获取使用排行失败: {str(e)}")
            return {
                'success': False,
                'error_message': f'获取排行失败: {str(e)}'
            }

    @staticmethod
    def get_role_usage_statistics(role_id: Optional[int] = None, 
                                days: int = 30) -> Dict[str, Any]:
        """
        获取角色使用统计
        
        Args:
            role_id: 角色ID（可选，不指定则获取所有角色统计）
            days: 统计天数
            
        Returns:
            Dict[str, Any]: 角色使用统计数据
        """
        try:
            start_date = datetime.now() - timedelta(days=days)
            
            # 构建查询条件
            internal_query = KnowledgeQueryLog.query.filter(
                KnowledgeQueryLog.created_at >= start_date
            )
            external_query = ExternalKnowledgeQueryLog.query.filter(
                ExternalKnowledgeQueryLog.created_at >= start_date
            )
            
            if role_id:
                internal_query = internal_query.filter_by(role_id=role_id)
                external_query = external_query.filter_by(role_id=role_id)
            
            # 获取统计数据
            role_stats = {}
            
            # 内部知识库使用统计
            internal_stats = db.session.query(
                Role.id,
                Role.name,
                func.count(KnowledgeQueryLog.id).label('internal_queries')
            ).outerjoin(KnowledgeQueryLog, and_(
                Role.id == KnowledgeQueryLog.role_id,
                KnowledgeQueryLog.created_at >= start_date
            )).group_by(Role.id).all()
            
            for stat in internal_stats:
                role_stats[stat.id] = {
                    'role_id': stat.id,
                    'role_name': stat.name,
                    'internal_queries': stat.internal_queries or 0,
                    'external_queries': 0,
                    'total_queries': stat.internal_queries or 0
                }
            
            # 外部知识库使用统计
            external_stats = db.session.query(
                Role.id,
                Role.name,
                func.count(ExternalKnowledgeQueryLog.id).label('external_queries')
            ).outerjoin(ExternalKnowledgeQueryLog, and_(
                Role.id == ExternalKnowledgeQueryLog.role_id,
                ExternalKnowledgeQueryLog.created_at >= start_date
            )).group_by(Role.id).all()
            
            for stat in external_stats:
                if stat.id in role_stats:
                    role_stats[stat.id]['external_queries'] = stat.external_queries or 0
                    role_stats[stat.id]['total_queries'] += stat.external_queries or 0
                else:
                    role_stats[stat.id] = {
                        'role_id': stat.id,
                        'role_name': stat.name,
                        'internal_queries': 0,
                        'external_queries': stat.external_queries or 0,
                        'total_queries': stat.external_queries or 0
                    }
            
            # 转换为列表并排序
            result = list(role_stats.values())
            result.sort(key=lambda x: x['total_queries'], reverse=True)
            
            return {
                'success': True,
                'data': result
            }
            
        except Exception as e:
            logger.error(f"获取角色使用统计失败: {str(e)}")
            return {
                'success': False,
                'error_message': f'获取统计失败: {str(e)}'
            }
